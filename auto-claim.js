import { ethers } from 'ethers';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import fs from 'fs';
import https from 'https';
import { HttpsProxyAgent } from 'https-proxy-agent';

dotenv.config();

const API_BASE = 'https://testnet-api.helioschain.network/api';

let proxyAgent = null;
let httpsAgent = null;

if (process.env.PROXY_URL) {
    const proxyOptions = {};
    
    if (process.env.IGNORE_SSL === 'true') {
        proxyOptions.rejectUnauthorized = false;
        console.log('⚠️ SSL verification disabled');
    }
    
    if (process.env.CA_CERT_PATH && fs.existsSync(process.env.CA_CERT_PATH)) {
        const caCert = fs.readFileSync(process.env.CA_CERT_PATH);
        proxyOptions.ca = caCert;
        console.log(`Using CA certificate: ${process.env.CA_CERT_PATH}`);
    }
    
    const finalOptions = {
        ...proxyOptions,
        rejectUnauthorized: process.env.IGNORE_SSL !== 'true'
    };
    
    if (process.env.IGNORE_SSL === 'true') {
        process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
        console.log('⚠️ NODE_TLS_REJECT_UNAUTHORIZED set to 0');
    }
    
    proxyAgent = new HttpsProxyAgent(process.env.PROXY_URL, finalOptions);
    console.log(`Using proxy: ${process.env.PROXY_URL.replace(/:([^:@]*@)/, ':***@')}`);
} else if (process.env.IGNORE_SSL === 'true') {
    httpsAgent = new https.Agent({
        rejectUnauthorized: false
    });
    console.log('⚠️ SSL verification disabled');
}

const HEADERS = {
    'accept': '*/*',
    'accept-language': 'id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'origin': 'https://testnet.helioschain.network',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://testnet.helioschain.network/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
};

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function setAgent(options) {
    if (proxyAgent) options.agent = proxyAgent;
    else if (httpsAgent) options.agent = httpsAgent;
    return options;
}

async function retryApiCall(apiCall, maxRetries = 10, delay = 2000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await apiCall();
            
            if (result && result.status && result.status >= 400) {
                if (result.status === 402) {
                    console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
                    return result;
                }
                
                if (attempt < maxRetries) {
                    console.log(`HTTP Error ${result.status} ${result.statusText || ''}, retrying... (${attempt}/${maxRetries})`);
                    await sleep(delay);
                    continue;
                }
                return result;
            }
            
            return result;
        } catch (error) {
            console.log(`API call error (attempt ${attempt}/${maxRetries}): ${error.message}`);
            if (attempt < maxRetries) {
                console.log(`Retrying in ${delay/1000} seconds...`);
                await sleep(delay);
            } else {
                throw error;
            }
        }
    }
}

async function signMessage(wallet, address) {
    const message = `Welcome to Helios! Please sign this message to verify your wallet ownership.

Wallet: ${address}`;
    return await wallet.signMessage(message);
}

async function checkUserExists(address, signature) {
    return await retryApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
                wallet: address,
                signature: signature
            })
        });
        
        const response = await fetch(`${API_BASE}/users/login`, options);
        
        if (response.status === 402) {
            console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
            return { success: false, message: 'Proxy policy error' };
        }
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function claimFaucet(token) {
    try {
        const options = setAgent({
            method: 'POST',
            headers: {
                ...HEADERS,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                token: 'HLS',
                chain: 'helios-testnet',
                amount: 1
            })
        });
        
        const response = await fetch(`${API_BASE}/faucet/request`, options);
        
        if (response.status === 402) {
            console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
            return { success: false, message: 'Proxy policy error' };
        }
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        const result = await response.json();
        
        // Don't retry if it's a cooldown error
        if (!result.success && result.message && result.message.includes('cooldown')) {
            return result; // Return immediately without retry
        }
        
        // Don't retry if it's "Not eligible" error
        if (!result.success && result.message && result.message.includes('Not eligible')) {
            return result; // Return immediately without retry
        }
        
        return result;
        
    } catch (error) {
        console.log(`Faucet API call error: ${error.message}`);
        throw error;
    }
}

function loadAccounts(filename) {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`File ${filename} not found. Creating empty file...`);
            fs.writeFileSync(filename, '');
            return [];
        }
        
        const data = fs.readFileSync(filename, 'utf8');
        const lines = data.split('\n').filter(line => line.trim() !== '');
        
        return lines.map(line => {
            const [address, privateKey] = line.split(', ');
            return { address: address?.trim(), privateKey: privateKey?.trim() };
        }).filter(account => account.address && account.privateKey);
    } catch (error) {
        console.log(`Error loading accounts from ${filename}: ${error.message}`);
        return [];
    }
}

function saveClaimData(data) {
    try {
        fs.writeFileSync('claim_data.json', JSON.stringify(data, null, 2));
    } catch (error) {
        console.log(`Error saving claim data: ${error.message}`);
    }
}

function loadClaimData() {
    try {
        if (fs.existsSync('claim_data.json')) {
            return JSON.parse(fs.readFileSync('claim_data.json', 'utf8'));
        }
    } catch (error) {
        console.log(`Error loading claim data: ${error.message}`);
    }
    return {};
}

async function claimForAccount(account) {
    try {
        console.log(`\n🔄 Processing claim for ${account.address}...`);
        
        const wallet = new ethers.Wallet(account.privateKey);
        const signature = await signMessage(wallet, account.address);
        
        const loginResult = await checkUserExists(account.address, signature);
        
        if (!loginResult || !loginResult.success) {
            if (loginResult && loginResult.status === 401) {
                console.log(`❌ Account ${account.address} is not registered`);
            } else {
                console.log(`❌ Login failed for ${account.address}`);
            }
            return false;
        }
        
        const token = loginResult.token;
        const faucetResult = await claimFaucet(token);
        if (faucetResult && faucetResult.success) {
            console.log(`✅ Faucet claimed successfully for ${account.address}`);
            console.log(`   Amount: ${faucetResult.amount || '1'} HLS`);
            return true;
        } else {
            if (faucetResult && faucetResult.message) {
                // Handle cooldown error specifically
                if (faucetResult.message.includes('cooldown') || faucetResult.message.includes('Not eligible')) {
                    console.log(`⏰ Faucet cooldown active for ${account.address}: ${faucetResult.message}`);
                } else {
                    console.log(`❌ Faucet claim failed for ${account.address}: ${faucetResult.message}`);
                }
            } else if (faucetResult && faucetResult.status) {
                console.log(`❌ Faucet claim failed for ${account.address}: HTTP ${faucetResult.status}`);
            } else {
                console.log(`❌ Faucet claim failed for ${account.address}: Unknown error`);
            }
            return false;
        }
    } catch (error) {
        console.log(`❌ Error claiming for ${account.address}: ${error.message}`);
        return false;
    }
}

async function processAllAccounts() {
    console.log('\n🚀 Starting auto claim process...');
    console.log(`🕐 Time: ${new Date().toLocaleString()}`);
    
    // Load accounts dari file
    const mainAccounts = loadAccounts('akun_utama.txt');
    const referralAccounts = loadAccounts('reff_akun_utama.txt');
    
    // Add master account from .env PRIVATE_KEY
    const masterAccounts = [];
    if (process.env.PRIVATE_KEY) {
        try {
            const masterWallet = new ethers.Wallet(process.env.PRIVATE_KEY);
            masterAccounts.push({
                address: masterWallet.address,
                privateKey: process.env.PRIVATE_KEY
            });
            console.log(`✅ Master account from .env added: ${masterWallet.address}`);
        } catch (error) {
            console.log(`❌ Error loading master account from .env: ${error.message}`);
        }
    }
    
    console.log(`📊 Loaded ${masterAccounts.length} master account, ${mainAccounts.length} main accounts and ${referralAccounts.length} referral accounts`);
    
    if (masterAccounts.length === 0 && mainAccounts.length === 0 && referralAccounts.length === 0) {
        console.log('❌ No accounts found. Please add PRIVATE_KEY to .env or accounts to akun_utama.txt and reff_akun_utama.txt');
        return;
    }
    
    let successCount = 0;
    let failCount = 0;
    
    // Process master account first
    if (masterAccounts.length > 0) {
        console.log('\n👑 Processing Master Account...');
        for (let i = 0; i < masterAccounts.length; i++) {
            const account = masterAccounts[i];
            console.log(`[${i + 1}/${masterAccounts.length}] Processing master account...`);
            
            const success = await claimForAccount(account);
            if (success) {
                successCount++;
            } else {
                failCount++;
            }
            
            // Delay between accounts to avoid rate limiting
            if (i < masterAccounts.length - 1) {
                console.log('⏳ Waiting 3 seconds before next account...');
                await sleep(3000);
            }
        }
    }
    
    // Process main accounts
    console.log('\n📋 Processing Main Accounts...');
    for (let i = 0; i < mainAccounts.length; i++) {
        const account = mainAccounts[i];
        console.log(`[${i + 1}/${mainAccounts.length}] Processing main account...`);
        
        const success = await claimForAccount(account);
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        
        // Delay between accounts to avoid rate limiting
        if (i < mainAccounts.length - 1) {
            console.log('⏳ Waiting 3 seconds before next account...');
            await sleep(3000);
        }
    }
    
    // Process referral accounts
    console.log('\n📋 Processing Referral Accounts...');
    for (let i = 0; i < referralAccounts.length; i++) {
        const account = referralAccounts[i];
        console.log(`[${i + 1}/${referralAccounts.length}] Processing referral account...`);
        
        const success = await claimForAccount(account);
        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        
        // Delay between accounts to avoid rate limiting
        if (i < referralAccounts.length - 1) {
            console.log('⏳ Waiting 3 seconds before next account...');
            await sleep(3000);
        }
    }
    
    const claimData = loadClaimData();
    claimData.lastClaim = new Date().toISOString();
    claimData.totalSuccess = (claimData.totalSuccess || 0) + successCount;
    claimData.totalFail = (claimData.totalFail || 0) + failCount;
    saveClaimData(claimData);
    
    console.log('\n📊 Claim Summary:');
    console.log(`✅ Successful claims: ${successCount}`);
    console.log(`❌ Failed claims: ${failCount}`);
    console.log(`📈 Total successful claims: ${claimData.totalSuccess}`);
    console.log(`📉 Total failed claims: ${claimData.totalFail}`);
    console.log(`⏰ Next claim in 24 hours: ${new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleString()}`);
}

function startAutoClaim() {
    console.log('🌟 Helios Auto Claim Faucet Bot Started!');
    console.log('⏰ Bot akan claim setiap 24 jam untuk semua akun utama dan referral');
    
    // Run immediately on start
    processAllAccounts();
    
    // Set interval for 24 hours (24 * 60 * 60 * 1000 ms)
    setInterval(() => {
        processAllAccounts();
    }, 24 * 60 * 60 * 1000);
    
    console.log('\n✅ Auto claim scheduler started!');
    console.log('   - Bot akan berjalan terus-menerus');
    console.log('   - Claim akan dilakukan setiap 24 jam');
    console.log('   - Tekan Ctrl+C untuk stop bot');
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Stopping auto claim bot...');
    console.log('✅ Bot stopped gracefully');
    process.exit(0);
});

process.unhandledRejection = (reason, promise) => {
    console.log('Unhandled Rejection at:', promise, 'reason:', reason);
};

process.on('uncaughtException', (error) => {
    console.log('Uncaught Exception:', error);
});

// Start the auto claim bot
startAutoClaim(); 