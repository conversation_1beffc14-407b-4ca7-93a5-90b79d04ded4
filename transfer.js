import { ethers } from 'ethers';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const HELIOS_TESTNET = {
    chainId: 42000,
    name: 'Helios Testnet',
    rpcUrl: 'https://testnet1.helioschainlabs.org',
    explorer: 'https://explorer.helioschainlabs.org'
};

const provider = new ethers.JsonRpcProvider(HELIOS_TESTNET.rpcUrl);

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function loadAccountsFromFile(filename) {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`File ${filename} not found`);
            return [];
        }
        
        const data = fs.readFileSync(filename, 'utf8');
        const lines = data.split('\n').filter(line => line.trim());
        
        const accounts = [];
        for (const line of lines) {
            const [address, privateKey] = line.split(',').map(s => s.trim());
            if (address && privateKey && address.startsWith('0x') && privateKey.startsWith('0x')) {
                accounts.push({ address, privateKey });
            }
        }
        
        console.log(`Loaded ${accounts.length} accounts from ${filename}`);
        return accounts;
    } catch (error) {
        console.log(`Error loading ${filename}: ${error.message}`);
        return [];
    }
}

function getAllAccounts() {
    const accounts = [];
    
    if (process.env.PRIVATE_KEY) {
        const masterWallet = new ethers.Wallet(process.env.PRIVATE_KEY);
        accounts.push({
            address: masterWallet.address,
            privateKey: process.env.PRIVATE_KEY,
            type: 'master'
        });
        console.log(`Added master account: ${masterWallet.address}`);
    }
    
    const mainAccounts = loadAccountsFromFile('akun_utama.txt');
    mainAccounts.forEach(acc => {
        accounts.push({ ...acc, type: 'main' });
    });
    
    const refAccounts = loadAccountsFromFile('reff_akun_utama.txt');
    refAccounts.forEach(acc => {
        accounts.push({ ...acc, type: 'referral' });
    });
    
    console.log(`Total accounts loaded: ${accounts.length}`);
    return accounts;
}

async function getBalance(address) {
    try {
        const balance = await provider.getBalance(address);
        return ethers.formatEther(balance);
    } catch (error) {
        console.log(`Error getting balance for ${address}: ${error.message}`);
        return '0';
    }
}

async function transfer(fromPrivateKey, toAddress, amount) {
    try {
        const wallet = new ethers.Wallet(fromPrivateKey, provider);
        const fromAddress = wallet.address;
        
        const balanceBefore = await getBalance(fromAddress);
        console.log(`Transfer: ${fromAddress} -> ${toAddress}`);
        console.log(`Amount: ${amount} HLS | Balance before: ${balanceBefore} HLS`);
        
        if (parseFloat(balanceBefore) < parseFloat(amount) + 0.01) {
            console.log(`❌ Insufficient balance for transfer + gas`);
            return false;
        }
        
        const tx = await wallet.sendTransaction({
            to: toAddress,
            value: ethers.parseEther(amount.toString()),
            gasLimit: 21000
        });
        
        console.log(`✓ Transaction sent: ${tx.hash}`);
        console.log(`Explorer: ${HELIOS_TESTNET.explorer}/tx/${tx.hash}`);
        
        const receipt = await tx.wait();
        console.log(`✓ Transaction confirmed in block ${receipt.blockNumber}`);
        
        const balanceAfter = await getBalance(fromAddress);
        console.log(`Balance after: ${balanceAfter} HLS\n`);
        
        return true;
    } catch (error) {
        console.log(`❌ Transfer failed: ${error.message}\n`);
        return false;
    }
}

function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

async function checkAllBalances(accounts) {
    console.log('=== Account Balances ===');
    let totalBalance = 0;
    
    for (const account of accounts) {
        const balance = await getBalance(account.address);
        console.log(`${account.type}: ${account.address} - ${balance} HLS`);
        totalBalance += parseFloat(balance);
        await sleep(100);
    }
    
    console.log(`Total Balance: ${totalBalance.toFixed(4)} HLS\n`);
    return totalBalance;
}

async function performRandomTransfers(accounts, rounds = 5) {
    console.log(`\n=== Starting ${rounds} rounds of random transfers ===\n`);
    
    let successfulTransfers = 0;
    let failedTransfers = 0;
    
    for (let round = 1; round <= rounds; round++) {
        console.log(`--- Round ${round}/${rounds} ---`);
        
        const shuffledAccounts = shuffleArray(accounts);
        
        for (let i = 0; i < shuffledAccounts.length; i++) {
            const fromAccount = shuffledAccounts[i];
            const toAccount = shuffledAccounts[(i + 1) % shuffledAccounts.length];
            
            if (fromAccount.address === toAccount.address) continue;
            
            const transferAmount = (Math.random() * 0.05 + 0.01).toFixed(4);
            
            const success = await transfer(fromAccount.privateKey, toAccount.address, transferAmount);
            
            if (success) {
                successfulTransfers++;
            } else {
                failedTransfers++;
            }
            
            const delay = Math.random() * 3000 + 2000;
            await sleep(delay);
        }
        
        console.log(`Round ${round} completed. Waiting before next round...\n`);
        await sleep(5000);
    }
    
    console.log('=== Transfer Summary ===');
    console.log(`Successful transfers: ${successfulTransfers}`);
    console.log(`Failed transfers: ${failedTransfers}`);
    console.log(`Total attempts: ${successfulTransfers + failedTransfers}`);
}

async function main() {
    console.log('=== Helios Testnet Auto Transfer Bot ===\n');
    
    console.log(`Network: ${HELIOS_TESTNET.name}`);
    console.log(`Chain ID: ${HELIOS_TESTNET.chainId}`);
    console.log(`RPC: ${HELIOS_TESTNET.rpcUrl}`);
    console.log(`Explorer: ${HELIOS_TESTNET.explorer}\n`);
    
    const accounts = getAllAccounts();
    
    if (accounts.length < 2) {
        console.log('❌ Need at least 2 accounts to perform transfers');
        return;
    }
    
    console.log('Checking network connectivity...');
    try {
        const network = await provider.getNetwork();
        console.log(`✓ Connected to network: ${network.name} (Chain ID: ${network.chainId})\n`);
    } catch (error) {
        console.log(`❌ Failed to connect to network: ${error.message}`);
        return;
    }
    
    await checkAllBalances(accounts);
    
    const validAccounts = [];
    for (const account of accounts) {
        const balance = await getBalance(account.address);
        if (parseFloat(balance) > 0.02) {
            validAccounts.push(account);
        } else {
            console.log(`Skipping ${account.address} - insufficient balance (${balance} HLS)`);
        }
    }
    
    if (validAccounts.length < 2) {
        console.log('❌ Need at least 2 accounts with sufficient balance');
        return;
    }
    
    console.log(`${validAccounts.length} accounts with sufficient balance for transfers\n`);
    
    await performRandomTransfers(validAccounts, 3);
    
    console.log('\n=== Final Balances ===');
    await checkAllBalances(accounts);
    
    console.log('✅ Auto transfer bot completed!');
}

main().catch(console.error); 