import { ethers } from 'ethers';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import fs from 'fs';
import readline from 'readline';
import { HttpsProxyAgent } from 'https-proxy-agent';
import https from 'https';

dotenv.config();

const API_BASE = 'https://testnet-api.helioschain.network/api';

let proxyAgent = null;
let httpsAgent = null;

if (process.env.PROXY_URL) {
    const proxyOptions = {};
    
    if (process.env.IGNORE_SSL === 'true') {
        proxyOptions.rejectUnauthorized = false;
        console.log('⚠️ SSL verification disabled');
    }
    
    if (process.env.CA_CERT_PATH && fs.existsSync(process.env.CA_CERT_PATH)) {
        const caCert = fs.readFileSync(process.env.CA_CERT_PATH);
        proxyOptions.ca = caCert;
        console.log(`Using CA certificate: ${process.env.CA_CERT_PATH}`);
    }
    
    const finalOptions = {
        ...proxyOptions,
        rejectUnauthorized: process.env.IGNORE_SSL !== 'true'
    };
    
    if (process.env.IGNORE_SSL === 'true') {
        process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
        console.log('⚠️ NODE_TLS_REJECT_UNAUTHORIZED set to 0');
    }
    
    proxyAgent = new HttpsProxyAgent(process.env.PROXY_URL, finalOptions);
    console.log(`Using proxy: ${process.env.PROXY_URL.replace(/:([^:@]*@)/, ':***@')}`);
} else if (process.env.IGNORE_SSL === 'true') {
    httpsAgent = new https.Agent({
        rejectUnauthorized: false
    });
    console.log('⚠️ SSL verification disabled');
}

const HEADERS = {
    'accept': '*/*',
    'accept-language': 'id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'origin': 'https://testnet.helioschain.network',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://testnet.helioschain.network/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
};

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

function setAgent(options) {
    if (proxyAgent) options.agent = proxyAgent;
    else if (httpsAgent) options.agent = httpsAgent;
    return options;
}

async function retryApiCall(apiCall, maxRetries = 10, delay = 2000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await apiCall();
            
            if (result && result.status && result.status >= 400) {
                if (result.status === 402) {
                    console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
                    return result;
                }
                
                if (attempt < maxRetries) {
                    console.log(`HTTP Error ${result.status} ${result.statusText || ''}, retrying... (${attempt}/${maxRetries})`);
                    await sleep(delay);
                    continue;
                }
                return result;
            }
            
            return result;
        } catch (error) {
            console.log(`API call error (attempt ${attempt}/${maxRetries}): ${error.message}`);
            if (attempt < maxRetries) {
                console.log(`Retrying in ${delay/1000} seconds...`);
                await sleep(delay);
            } else {
                throw error;
            }
        }
    }
}

async function authApiCall(apiCall, maxRetries = 10, delay = 2000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await apiCall();
            
            if (result && result.status && result.status >= 400) {
                if (result.status === 402) {
                    console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
                    return result;
                }
                
                // 401 is expected for new accounts (don't retry)
                if (result.status === 401) {
                    return result;
                }
                
                // Retry other errors (400, 429, 500, etc.)
                if (attempt < maxRetries) {
                    console.log(`HTTP Error ${result.status} ${result.statusText || ''}, retrying... (${attempt}/${maxRetries})`);
                    await sleep(delay);
                    continue;
                }
                return result;
            }
            
            return result;
        } catch (error) {
            console.log(`API call error (attempt ${attempt}/${maxRetries}): ${error.message}`);
            if (attempt < maxRetries) {
                console.log(`Retrying in ${delay/1000} seconds...`);
                await sleep(delay);
            } else {
                throw error;
            }
        }
    }
}

async function testProxy() {
    if (!proxyAgent) return true;
    
    try {
        console.log('Testing proxy connection...');
        const options = setAgent({
            method: 'GET',
            timeout: 10000
        });
        
        const response = await fetch('https://geo.brdtest.com/welcome.txt', options);
        
        if (response.status === 407) {
            console.log('❌ Proxy authentication failed!');
            console.log('Please check your proxy credentials in .env file');
            return false;
        }
        
        if (response.ok) {
            const text = await response.text();
            console.log('✅ Proxy connection successful');
            return true;
        } else {
            console.log(`❌ Proxy test failed with status: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ Proxy test error: ${error.message}`);
        return false;
    }
}

async function signMessage(wallet, address) {
    const message = `Welcome to Helios! Please sign this message to verify your wallet ownership.

Wallet: ${address}`;
    return await wallet.signMessage(message);
}

async function getUserStatus(token, address) {
    return await retryApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: {
                ...HEADERS,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                wallet: address
            })
        });
        
        const response = await fetch(`${API_BASE}/invite-quota/user-status`, options);
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function checkUserExists(wallet, signature) {
    return await authApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
                wallet: wallet,
                signature
            })
        });
        
        const response = await fetch(`${API_BASE}/users/login`, options);
        
        if (response.status === 402) {
            console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
            return { success: false, message: 'Proxy policy error' };
        }
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function registerUser(wallet, signature, inviteCode = null) {
    return await authApiCall(async () => {
        const payload = {
            wallet: wallet,
            signature
        };
        
        if (inviteCode) {
            payload.inviteCode = inviteCode;
        }

        const options = setAgent({
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify(payload)
        });

        const response = await fetch(`${API_BASE}/users/confirm-account`, options);
        
        if (response.status === 402) {
            console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
            return { success: false, message: 'Proxy policy error' };
        }
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function startOnboarding(token, stepKey) {
    return await retryApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: {
                ...HEADERS,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ stepKey })
        });
        
        const response = await fetch(`${API_BASE}/users/onboarding/start`, options);
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function completeOnboarding(token, stepKey, evidence) {
    return await retryApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: {
                ...HEADERS,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ stepKey, evidence })
        });
        
        const response = await fetch(`${API_BASE}/users/onboarding/complete`, options);
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function claimFaucet(token) {
    return await retryApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: {
                ...HEADERS,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                token: 'HLS',
                chain: 'helios-testnet',
                amount: 1
            })
        });
        
        const response = await fetch(`${API_BASE}/faucet/request`, options);
        
        if (response.status === 402) {
            console.log('❌ Proxy Error 402: POST requests blocked by residential proxy');
            return { success: false, message: 'Proxy policy error' };
        }
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

async function claimOnboardingReward(token) {
    return await retryApiCall(async () => {
        const options = setAgent({
            method: 'POST',
            headers: {
                ...HEADERS,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ rewardType: 'xp' })
        });
        
        const response = await fetch(`${API_BASE}/users/onboarding/claim-reward`, options);
        
        if (!response.ok) {
            return { status: response.status, statusText: response.statusText };
        }
        
        return await response.json();
    });
}

function appendAccountToFile(filename, account) {
    const data = `${account.address}, ${account.privateKey}\n`;
    
    try {
        fs.appendFileSync(filename, data);
        console.log(`✓ Account saved to ${filename}: ${account.address}`);
    } catch (error) {
        console.log(`Error saving account: ${error.message}`);
    }
}

async function processAccount(wallet, inviteCode = null, accountType = 'main') {
    const address = wallet.address;
    console.log(`Processing account: ${address}`);
    
    try {
        const signature = await signMessage(wallet, address);
        
        let loginResult = await checkUserExists(address, signature);
        let token;
        let referralCode;
        
        if (loginResult && loginResult.success) {
            token = loginResult.token;
            referralCode = loginResult.user.referralCode;
            console.log(`✓ Login successful for ${address}`);
        } else if (loginResult && loginResult.status === 401) {
            console.log(`Account not exists, registering ${address}...`);
            const registerResult = await registerUser(address, signature, inviteCode);
            if (registerResult && registerResult.success) {
                token = registerResult.token;
                referralCode = registerResult.user.referralCode;
                console.log(`✓ Registration successful for ${address}`);
            } else {
                if (registerResult && registerResult.status) {
                    console.log(`✗ Registration failed for ${address} - HTTP ${registerResult.status}: ${registerResult.statusText || 'Unknown error'}`);
                } else if (registerResult && registerResult.message) {
                    console.log(`✗ Registration failed for ${address} - ${registerResult.message}`);
                } else {
                    console.log(`✗ Registration failed for ${address} - Unknown error`);
                }
                return null;
            }
        } else {
            if (loginResult && loginResult.status) {
                console.log(`✗ Login failed for ${address} - HTTP ${loginResult.status}: ${loginResult.statusText || 'Unknown error'}`);
            } else {
                console.log(`✗ Login failed for ${address} - Unknown error`);
            }
            return null;
        }
        
        console.log(`Starting onboarding for ${address}...`);
        
        await startOnboarding(token, 'add_helios_network');
        await sleep(1000);
        await completeOnboarding(token, 'add_helios_network', 'network_added');
        console.log(`✓ Step 1 completed for ${address}`);
        
        await sleep(1000);
        await startOnboarding(token, 'claim_from_faucet');
        await sleep(1000);
        const faucetResult = await claimFaucet(token);
        if (faucetResult && faucetResult.success) {
            console.log(`✓ Faucet claimed for ${address}`);
            await sleep(1000);
            await completeOnboarding(token, 'claim_from_faucet', 'tokens_claimed');
            console.log(`✓ Step 2 completed for ${address}`);
        }
        
        await sleep(1000);
        await startOnboarding(token, 'mint_early_bird_nft');
        await sleep(1000);
        await completeOnboarding(token, 'mint_early_bird_nft', 'nft_minted');
        console.log(`✓ Step 3 completed for ${address}`);
        
        await sleep(1000);
        await claimOnboardingReward(token);
        console.log(`✓ Onboarding reward claimed for ${address}`);
        
        const accountData = {
            address,
            privateKey: wallet.privateKey,
            referralCode,
            token
        };
        
        const filename = accountType === 'main' ? 'akun_utama.txt' : 'reff_akun_utama.txt';
        appendAccountToFile(filename, accountData);
        
        return accountData;
        
    } catch (error) {
        console.log(`Error processing ${address}: ${error.message}`);
        return null;
    }
}

async function getMasterAccountInfo() {
    const masterWallet = new ethers.Wallet(process.env.PRIVATE_KEY);
    const signature = await signMessage(masterWallet, masterWallet.address);
    
    const loginResult = await checkUserExists(masterWallet.address, signature);
    if (loginResult && loginResult.success) {
        const statusResult = await getUserStatus(loginResult.token, masterWallet.address);
        return {
            referralCode: loginResult.user.referralCode,
            remainingInvites: statusResult?.data?.remainingInvites || 0,
            usedToday: statusResult?.data?.usedToday || 0,
            currentQuota: statusResult?.data?.currentQuota || 5,
            token: loginResult.token
        };
    }
    
    return null;
}

function saveProgress(walletCount, currentIndex, mainAccounts, referralAccounts) {
    const progress = {
        walletCount,
        currentIndex,
        mainAccounts,
        referralAccounts,
        timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync('progress.json', JSON.stringify(progress, null, 2));
    if (walletCount === 0) {
        console.log(`Progress saved: Infinite mode - ${mainAccounts.length} main, ${referralAccounts.length} referral accounts`);
    } else {
        console.log(`Progress saved: ${currentIndex}/${walletCount} main accounts created`);
    }
}

function loadProgress() {
    try {
        if (fs.existsSync('progress.json')) {
            const progress = JSON.parse(fs.readFileSync('progress.json', 'utf8'));
            if (progress.walletCount === 0) {
                console.log(`Found previous progress: Infinite mode - ${progress.mainAccounts?.length || 0} main, ${progress.referralAccounts?.length || 0} referral accounts`);
            } else {
                console.log(`Found previous progress: ${progress.currentIndex}/${progress.walletCount} main accounts`);
            }
            return progress;
        }
    } catch (error) {
        console.log('Error loading progress:', error.message);
    }
    return null;
}

function clearProgress() {
    try {
        if (fs.existsSync('progress.json')) {
            fs.unlinkSync('progress.json');
            console.log('Progress file cleared');
        }
    } catch (error) {
        console.log('Error clearing progress:', error.message);
    }
}

async function main() {
    console.log('=== Helios Testnet Bot with Ponzi Referral System ===\n');
    
    if (!process.env.PRIVATE_KEY) {
        console.log('Error: PRIVATE_KEY not found in .env file');
        rl.close();
        return;
    }
    
    if (proxyAgent) {
        const proxyWorking = await testProxy();
        if (!proxyWorking) {
            console.log('\nProxy test failed. Please fix proxy settings before continuing.');
            console.log('Or disable proxy by removing PROXY_URL from .env file');
            rl.close();
            return;
        }
    }
    
    console.log('Checking master account...');
    const masterAccount = await getMasterAccountInfo();
    
    if (!masterAccount) {
        console.log('\n❌ Master account not found!');
        console.log('Please create your main account first at: https://testnet.helioschain.network/');
        console.log('Then run this bot again.');
        rl.close();
        return;
    }
    
    console.log(`✓ Master account found!`);
    console.log(`Referral Code: ${masterAccount.referralCode}`);
    console.log(`Total Quota: ${masterAccount.currentQuota}`);
    console.log(`Used Today: ${masterAccount.usedToday}`);
    console.log(`Remaining Invites: ${masterAccount.remainingInvites}`);
    
    console.log('\n=== 🔥 INFINITE PONZI SYSTEM 🔥 ===');
    console.log('This bot will create accounts continuously using referral system:');
    console.log('• Master account creates main accounts (using remaining quota)');
    console.log('• Each main account creates 5 referral accounts');
    console.log('• Each referral account becomes new main account');
    console.log('• Process repeats infinitely until you stop (Ctrl+C)');
    console.log('• All accounts require valid invite codes (no empty codes allowed)');
    console.log('==========================================\n');
    
    const continueBot = await question('Start infinite ponzi bot? (y/n): ');
    if (continueBot.toLowerCase() !== 'y') {
        console.log('Bot cancelled.');
        rl.close();
        return;
    }
    
    let startIndex = 0;
    let mainAccounts = [];
    let referralAccounts = [];
    
    const existingProgress = loadProgress();
    if (existingProgress) {
        const resume = await question('\nFound previous progress. Resume? (y/n): ');
        if (resume.toLowerCase() === 'y') {
            startIndex = existingProgress.currentIndex;
            mainAccounts = existingProgress.mainAccounts || [];
            referralAccounts = existingProgress.referralAccounts || [];
            console.log(`Resuming from main account index ${startIndex}`);
        } else {
            clearProgress();
        }
    }
    
    console.log('\n=== 🚀 Starting Infinite Ponzi System ===');
    
    let currentMainIndex = startIndex;
    let availableMainAccounts = [...mainAccounts];
    
    // Add master account as first main account if not already added
    if (availableMainAccounts.length === 0) {
        availableMainAccounts.push({
            address: new ethers.Wallet(process.env.PRIVATE_KEY).address,
            referralCode: masterAccount.referralCode,
            remainingInvites: masterAccount.remainingInvites
        });
    }
    
    console.log('\n🎯 Infinite Ponzi Loop Started - Press Ctrl+C to stop');
    console.log('Each cycle: 1 main account → 5 referral accounts → 5 new main accounts → repeat\n');
    
    while (true) {
        try {
            // Check for accounts with remaining invites
            const accountsWithInvites = availableMainAccounts.filter(acc => 
                acc.remainingInvites && acc.remainingInvites > 0
            );
            
            // If no accounts with invites, promote referral accounts to main accounts
            if (accountsWithInvites.length === 0) {
                if (referralAccounts.length === 0) {
                    console.log('\n⚠️ No accounts available. Waiting for rate limit to reset...');
                    await sleep(60000); // Wait 1 minute for rate limit
                    continue;
                }
                
                console.log('\n🔄 Promoting referral accounts to main accounts...');
                // Move all referral accounts to main accounts pool
                for (const refAcc of referralAccounts) {
                    availableMainAccounts.push({
                        ...refAcc,
                        remainingInvites: 5 // Each account gets 5 invites
                    });
                }
                console.log(`✅ Promoted ${referralAccounts.length} referral accounts to main accounts`);
                referralAccounts = [];
                continue;
            }
            
            const currentMainAccount = accountsWithInvites[0];
            console.log(`\n=== Using Main Account: ${currentMainAccount.address} ===`);
            console.log(`Remaining invites: ${currentMainAccount.remainingInvites}`);
            
            // Create 5 referral accounts using current main account
            console.log(`\n=== Creating 5 Referral Accounts ===`);
            let successfulReferrals = 0;
            
            for (let j = 0; j < 5 && currentMainAccount.remainingInvites > 0; j++) {
                const refWallet = ethers.Wallet.createRandom();
                console.log(`Creating referral account ${j + 1}/5: ${refWallet.address}`);
                
                const refResult = await processAccount(refWallet, currentMainAccount.referralCode, 'referral');
                if (refResult) {
                    referralAccounts.push(refResult);
                    successfulReferrals++;
                    console.log(`✓ Referral account ${j + 1}/5 created successfully`);
                    
                    // Decrease remaining invites
                    currentMainAccount.remainingInvites--;
                } else {
                    console.log(`❌ Failed to create referral account ${j + 1}/5`);
                    
                    // If we hit rate limit (429), wait longer
                    if (refResult === null) {
                        console.log('⏳ Rate limit detected, waiting 30 seconds...');
                        await sleep(30000);
                        break; // Exit referral creation loop
                    }
                }
                
                await sleep(3000); // Wait between each referral account
            }
            
            // Remove account from pool if no invites left
            if (currentMainAccount.remainingInvites <= 0) {
                availableMainAccounts.shift();
                console.log(`✅ Main account ${currentMainAccount.address} exhausted all invites`);
            }
            
            currentMainIndex++;
            saveProgress(0, currentMainIndex, mainAccounts, referralAccounts);
            
            console.log(`\n📊 Current Stats:`);
            console.log(`• Main accounts available: ${availableMainAccounts.length}`);
            console.log(`• Total main accounts created: ${mainAccounts.length}`);
            console.log(`• Total referral accounts created: ${referralAccounts.length}`);
            console.log(`• Total accounts: ${mainAccounts.length + referralAccounts.length}`);
            console.log(`• Referrals created this cycle: ${successfulReferrals}/5`);
            
            // If we created less than expected, increase wait time
            if (successfulReferrals < 3) {
                console.log('⏳ Low success rate, waiting 60 seconds before next cycle...');
                await sleep(60000);
            } else {
                console.log('⏳ Waiting 10 seconds before next cycle...');
                await sleep(10000);
            }
            
        } catch (error) {
            console.log(`❌ Error in ponzi cycle: ${error.message}`);
            console.log('⏳ Waiting 30 seconds before retry...');
            await sleep(30000);
        }
    }
}

main().catch(console.error); 