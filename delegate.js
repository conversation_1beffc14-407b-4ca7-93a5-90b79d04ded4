import { ethers } from 'ethers';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const HELIOS_TESTNET = {
    chainId: 42000,
    name: 'Helios Testnet',
    rpcUrl: 'https://testnet1.helioschainlabs.org',
    explorer: 'https://explorer.helioschainlabs.org'
};

const STAKING_CONTRACT_ADDRESS = "******************************************";
const STAKING_ABI = [
    "function delegate(address delegator, address validator, uint256 amount, string symbol) public returns (bool)"
];

const VALIDATORS = [
    { moniker: "helios-hedge", address: "******************************************" },
    { moniker: "helios-supra", address: "******************************************" }
];

const DELEGATE_AMOUNT = "0.3"; // HLS amount to delegate
const TOKEN_SYMBOL = "ahelios";
const DECIMALS = 18;

const provider = new ethers.JsonRpcProvider(HELIOS_TESTNET.rpcUrl);

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function loadAccountsFromFile(filename) {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`File ${filename} not found`);
            return [];
        }
        
        const data = fs.readFileSync(filename, 'utf8');
        const lines = data.split('\n').filter(line => line.trim());
        
        const accounts = [];
        for (const line of lines) {
            const [address, privateKey] = line.split(',').map(s => s.trim());
            if (address && privateKey && address.startsWith('0x') && privateKey.startsWith('0x')) {
                accounts.push({ address, privateKey });
            }
        }
        
        console.log(`Loaded ${accounts.length} accounts from ${filename}`);
        return accounts;
    } catch (error) {
        console.log(`Error loading ${filename}: ${error.message}`);
        return [];
    }
}

function getAllAccounts() {
    const accounts = [];
    
    if (process.env.PRIVATE_KEY) {
        const masterWallet = new ethers.Wallet(process.env.PRIVATE_KEY);
        accounts.push({
            address: masterWallet.address,
            privateKey: process.env.PRIVATE_KEY,
            type: 'master'
        });
        console.log(`Added master account: ${masterWallet.address}`);
    }
    
    const mainAccounts = loadAccountsFromFile('akun_utama.txt');
    mainAccounts.forEach(acc => {
        accounts.push({ ...acc, type: 'main' });
    });
    
    const refAccounts = loadAccountsFromFile('reff_akun_utama.txt');
    refAccounts.forEach(acc => {
        accounts.push({ ...acc, type: 'referral' });
    });
    
    console.log(`Total accounts loaded: ${accounts.length}`);
    return accounts;
}

async function getBalance(address) {
    try {
        const balance = await provider.getBalance(address);
        return ethers.formatEther(balance);
    } catch (error) {
        console.log(`Error getting balance for ${address}: ${error.message}`);
        return '0';
    }
}

async function checkDelegations(address) {
    try {
        const payload = {
            jsonrpc: "2.0",
            method: "eth_getDelegations",
            params: [address],
            id: 1
        };

        const response = await fetch(HELIOS_TESTNET.rpcUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Origin': 'https://portal.helioschain.network'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.error) {
            throw new Error(`RPC Error: ${data.error.message}`);
        }

        const delegations = data.result || [];
        const delegationStatus = {};
        
        for (const validator of VALIDATORS) {
            const delegation = delegations.find(d => 
                d.validatorAddress.toLowerCase() === validator.address.toLowerCase()
            );
            
            const hasStake = delegation && delegation.shares && 
                             parseFloat(delegation.shares) > 0;
            
            delegationStatus[validator.address] = {
                moniker: validator.moniker,
                hasStake,
                shares: delegation?.shares || "0",
                totalBoost: delegation?.totalBoost || "0"
            };
        }
        
        return delegationStatus;
    } catch (error) {
        console.log(`Error checking delegations for ${address}: ${error.message}`);
        return {};
    }
}

async function delegateToValidator(privateKey, validatorAddress, validatorMoniker) {
    try {
        const wallet = new ethers.Wallet(privateKey, provider);
        const address = wallet.address;
        
        console.log(`\nDelegating 0.3 HLS to ${validatorMoniker}`);
        console.log(`From: ${address}`);
        console.log(`To: ${validatorAddress}`);
        
        const balance = await getBalance(address);
        const balanceNum = parseFloat(balance);
        
        if (balanceNum < 0.35) { // Need extra for gas
            console.log(`❌ Insufficient balance: ${balance} HLS (need at least 0.35 HLS)`);
            return false;
        }
        
        const contract = new ethers.Contract(STAKING_CONTRACT_ADDRESS, STAKING_ABI, wallet);
        const amount = ethers.parseUnits(DELEGATE_AMOUNT, DECIMALS);
        
        const tx = await contract.delegate(
            address,
            validatorAddress,
            amount,
            TOKEN_SYMBOL,
            { gasLimit: 1500000 }
        );
        
        console.log(`✓ Transaction sent: ${tx.hash}`);
        console.log(`Explorer: ${HELIOS_TESTNET.explorer}/tx/${tx.hash}`);
        
        const receipt = await tx.wait();
        console.log(`✅ Delegation confirmed in block ${receipt.blockNumber}`);
        
        return true;
    } catch (error) {
        console.log(`❌ Delegation failed: ${error.message}`);
        return false;
    }
}

async function processAccountDelegation(account) {
    console.log(`\n=== Processing ${account.type}: ${account.address} ===`);
    
    const balance = await getBalance(account.address);
    console.log(`Balance: ${balance} HLS`);
    
    if (parseFloat(balance) < 0.65) { // Need 0.6 HLS + gas for both delegations
        console.log(`❌ Insufficient balance for delegations (need at least 0.65 HLS)`);
        return { success: 0, failed: 2 };
    }
    
    console.log(`Checking current delegations...`);
    const delegations = await checkDelegations(account.address);
    
    let successCount = 0;
    let failedCount = 0;
    
    for (const validator of VALIDATORS) {
        const status = delegations[validator.address];
        
        if (status && status.hasStake) {
            console.log(`✓ Already delegated to ${status.moniker} (shares: ${status.shares})`);
            successCount++;
        } else {
            console.log(`No delegation found for ${validator.moniker}, proceeding...`);
            
            const success = await delegateToValidator(
                account.privateKey,
                validator.address,
                validator.moniker
            );
            
            if (success) {
                successCount++;
            } else {
                failedCount++;
            }
            
            // Wait between delegations
            await sleep(3000);
        }
    }
    
    console.log(`Account summary: ${successCount} successful, ${failedCount} failed`);
    return { success: successCount, failed: failedCount };
}

async function main() {
    console.log('=== Helios Testnet Auto Delegation Bot ===\n');
    
    console.log(`Network: ${HELIOS_TESTNET.name}`);
    console.log(`Chain ID: ${HELIOS_TESTNET.chainId}`);
    console.log(`RPC: ${HELIOS_TESTNET.rpcUrl}`);
    console.log(`Staking Contract: ${STAKING_CONTRACT_ADDRESS}`);
    console.log(`Delegation Amount: ${DELEGATE_AMOUNT} HLS per validator\n`);
    
    console.log('Validators:');
    VALIDATORS.forEach((v, i) => {
        console.log(`${i + 1}. ${v.moniker}: ${v.address}`);
    });
    console.log('');
    
    const accounts = getAllAccounts();
    
    if (accounts.length === 0) {
        console.log('❌ No accounts found');
        return;
    }
    
    console.log('Checking network connectivity...');
    try {
        const network = await provider.getNetwork();
        console.log(`✓ Connected to network: ${network.name} (Chain ID: ${network.chainId})\n`);
    } catch (error) {
        console.log(`❌ Failed to connect to network: ${error.message}`);
        return;
    }
    
    let totalSuccess = 0;
    let totalFailed = 0;
    let processedAccounts = 0;
    
    for (const account of accounts) {
        try {
            const result = await processAccountDelegation(account);
            totalSuccess += result.success;
            totalFailed += result.failed;
            processedAccounts++;
            
            console.log(`\n📊 Progress: ${processedAccounts}/${accounts.length} accounts processed`);
            console.log(`Total successful delegations: ${totalSuccess}`);
            console.log(`Total failed delegations: ${totalFailed}`);
            
            // Wait between accounts
            await sleep(5000);
            
        } catch (error) {
            console.log(`❌ Error processing account ${account.address}: ${error.message}`);
            totalFailed += 2; // Assume both validators failed
        }
    }
    
    console.log('\n=== Final Summary ===');
    console.log(`Accounts processed: ${processedAccounts}`);
    console.log(`Total successful delegations: ${totalSuccess}`);
    console.log(`Total failed delegations: ${totalFailed}`);
    console.log(`Expected delegations: ${accounts.length * 2} (${accounts.length} accounts × 2 validators)`);
    
    const successRate = ((totalSuccess / (accounts.length * 2)) * 100).toFixed(1);
    console.log(`Success rate: ${successRate}%`);
    
    console.log('\n✅ Auto delegation bot completed!');
}

main().catch(console.error); 